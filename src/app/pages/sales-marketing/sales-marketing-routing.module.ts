import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { CbdDiscountComponent } from './discount-setting/cbd-discount/cbd-discount.component';
import { CbdHistoryComponent } from './discount-setting/cbd-discount/cbd-history/cbd-history.component';
import { SalesDiscountComponent } from './discount-setting/sales-discount/sales-discount.component';
import { SalesHistoryComponent } from './discount-setting/sales-discount/sales-history/sales-history.component';
import { SalesFormSettingsComponent } from './discount-setting/sales-discount/sales-form-settings/sales-form-settings.component';
import { ProgramMarketingLegacyComponent } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-legacy.component';
import { DetailProgramMarketingComponent } from '@pages/sales-marketing/program-marketing-legacy/detail-program-marketing/detail-program-marketing.component';
import { ProgramMarketingFormComponent } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-form/program-marketing-form.component';
import { CreateSpmProgramMarketingComponent } from '@pages/sales-marketing/program-marketing-legacy/create-spm-program-marketing/create-spm-program-marketing.component';

const routes: Routes = [
  {
    path: 'discount-setting',
    redirectTo: 'discount-setting/sales',
    pathMatch: 'full',
  },
  {
    path: 'discount-setting/cbd',
    component: CbdDiscountComponent,
    data: {
      enum: 'CBD_DISCOUNT',
    },
  },
  {
    path: 'discount-setting/cbd-form-settings',
    loadChildren: () => import('./discount-setting/cbd-form-settings/cbd-form-settings.module').then((m) => m.CbdFormSettingsModule),
  },
  {
    path: 'discount-setting/cbd/history',
    component: CbdHistoryComponent,
  },
  {
    path: 'discount-setting/sales',
    component: SalesDiscountComponent,
    data: {
      enum: 'SALES_DISCOUNT',
    },
  },
  {
    path: 'discount-setting/sales/form',
    component: SalesFormSettingsComponent,
  },
  {
    path: 'discount-setting/sales/history',
    component: SalesHistoryComponent,
  },

  {
    path: 'program-marketing/list',
    component: ProgramMarketingLegacyComponent,
  },

  {
    path: 'program-marketing/form',
    component: ProgramMarketingFormComponent,
  },
  {
    path: 'program-marketing/form/:id',
    component: ProgramMarketingFormComponent,
  },

  {
    path: 'program-marketing/:id',
    component: DetailProgramMarketingComponent,
  },

  {
    path: 'program-marketing/create-spm/:id',
    component: CreateSpmProgramMarketingComponent,
  },

  {
    path: 'program-marketing-retailer',
    loadChildren: () => import('./program-marketing-retailer/program-marketing-retailer.module').then((m) => m.ProgramMarketingRetailerModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SalesMarketingRoutingModule {}
