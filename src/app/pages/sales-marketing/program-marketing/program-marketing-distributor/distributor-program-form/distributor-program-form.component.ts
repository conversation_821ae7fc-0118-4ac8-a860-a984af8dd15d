import { Component, OnInit, ViewChild } from '@angular/core';
import { Activated<PERSON>out<PERSON>, Params, Router } from '@angular/router';
import { PageInfoService, PageLink } from '@metronic/layout';
import { UtilitiesService } from '@services/utilities.service';
import {
  EnumProgramMarketingDiscountCategory,
  EnumProgramMarketingDiscountCategoryString,
  EnumProgramMarketingDiscountTypeString,
  EnumProgramMarketingPOType,
  EnumProgramMarketingRewardType,
  EnumProgramMarketingType,
  EnumProgramMarketingTypeString,
} from '../../../program-marketing-legacy/program-marketing.enum';
import { ProgramInformationFormComponent } from '../../../program-marketing-legacy/program-marketing-form/components/program-information-form/program-information-form.component';
import { ProgramTermFormComponent } from '../../../program-marketing-legacy/program-marketing-form/components/program-term-form/program-term-form.component';
import { OrderTermFormComponent } from '../../../program-marketing-legacy/program-marketing-form/components/order-term-form/order-term-form.component';
import { DiscountTermFormComponent } from '../../../program-marketing-legacy/program-marketing-form/components/discount-term-form/discount-term-form.component';
import { RewardTermFormComponent } from '../../../program-marketing-legacy/program-marketing-form/components/reward-term-form/reward-term-form.component';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { IPayloadProgramMarketing, IResponseProgramMarketing } from '../../../program-marketing-legacy/program-marketing.interface';
import { DistributorProgramFormService } from './distributor-program-form.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject, Observable, of, switchMap, tap } from 'rxjs';
import { CompensationTermFormComponent } from '../../../program-marketing-legacy/program-marketing-form/components/compensation-term-form/compensation-term-form.component';
import { BaseDatasource } from '@shared/base/base.datasource';
import { TableColumn } from '@shared/interface/table.interface';
import { finalize } from 'rxjs/operators';
import { ModalConfirmationProgramMarketingComponent } from '@pages/sales-marketing/program-marketing-legacy/program-marketing-form/components/modal-confirmation-program-marketing/modal-confirmation-program-marketing.component';

@Component({
  selector: 'app-program-marketing-form',
  templateUrl: './distributor-program-form.component.html',
  styleUrls: ['./distributor-program-form.component.scss'],
})
export class DistributorProgramFormComponent implements OnInit {
  params!: Params;
  programID!: string;

  dataResponse = new BehaviorSubject<IResponseProgramMarketing>({} as IResponseProgramMarketing);
  messageResponse = new BehaviorSubject('');

  detailProgram!: IPayloadProgramMarketing;
  isLoadingSubject: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  baseDataSourceList: BaseDatasource<any>;
  displayedColumns!: string[];
  tableColumns: TableColumn[] = [
    {
      key: 'name',
      title: 'PRODUK',
      isSortable: false,
    },
    {
      key: 'maximum_discount',
      title: 'MAKSIMAL DISKON',
      isSortable: false,
    },
    {
      key: 'minimum_order',
      title: 'MINIMAL PEMBELIAN',
      isSortable: false,
    },
  ];

  @ViewChild('programInfoRef') programInfoRef: ProgramInformationFormComponent;
  @ViewChild('programTermRef') programTermRef: ProgramTermFormComponent;
  @ViewChild('orderTermRef') orderTermRef: OrderTermFormComponent;
  @ViewChild('discountTermRef') discountTermRef: DiscountTermFormComponent;
  @ViewChild('rewardTermRef') rewardTermRef: RewardTermFormComponent;
  @ViewChild('compensationTermRef') compensationTermRef: CompensationTermFormComponent;

  @ViewChild('modalConfirmation') private modalConfirmation: ModalConfirmationProgramMarketingComponent;
  modalConfigConfirmation: ModalConfig = {
    modalTitle: 'TAMBAH PROGRAM MARKETING ONE SHOOT',
    closeButtonLabel: 'Batal',
    dismissButtonLabel: 'Lanjutkan',
    onDismiss: () => this.onSubmitForm(),
  };

  @ViewChild('modalResponse') private modalResponse: ModalComponent;
  modalConfigResponse: ModalConfig = {
    showHeader: false,
    closeButtonLabel: 'Lihat Detail',
    dismissButtonLabel: 'Oke',
    onClose: () => this.redirectToDetailPage(),
    onDismiss: () => this.redirectToListPage(),
    disableDismissButton: () => false,
    disableCloseButton: () => false,
  };

  get ProgramInfoForm() {
    return this.programInfoRef && this.programInfoRef.form;
  }

  get ProgramTermForm() {
    return this.programTermRef && this.programTermRef.form;
  }

  get OrderTermForm() {
    return this.orderTermRef && this.orderTermRef.form;
  }

  get DiscountTermForm() {
    return this.discountTermRef && this.discountTermRef.form;
  }

  get RewardTermForm() {
    return this.rewardTermRef && this.rewardTermRef.form;
  }

  get CompensationTermForm() {
    return this.compensationTermRef && this.compensationTermRef.form;
  }

  get FormTitle() {
    return this.utils.mapKeyToString(EnumProgramMarketingTypeString, this.params.type);
  }

  links: Array<PageLink> = [
    {
      title: 'Sales & Marketing',
      path: '',
      isActive: false,
    },
    this.pageInfoService.BreadcrumbSeparator,
    {
      title: 'Program Marketing',
      path: 'sales-marketing/program-marketing/list',
      isActive: false,
    },
    this.pageInfoService.BreadcrumbSeparator,
  ];

  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    public utils: UtilitiesService,
    public formService: DistributorProgramFormService
  ) {
    this.programID = this.activeRoute.snapshot.params['id'];
  }

  ngOnInit() {
    this.params = this.activeRoute.snapshot.queryParams;
    if (!this.params.type) return this.handleCancel();
    this.modalConfigConfirmation.modalTitle = 'TAMBAH PROGRAM MARKETING ' + this.FormTitle;
    this.getDataUpdate()
      .pipe(switchMap(async () => this.initPageInfo()))
      .subscribe();
  }

  getDataUpdate() {
    this.isLoadingSubject.next(true);

    if (!this.programID) {
      this.isLoadingSubject.next(false);
      this.initPageInfo();
      return of();
    }

    const updateMethods: Record<EnumProgramMarketingType, (id: string) => Observable<any>> = {
      [EnumProgramMarketingType.ONE_SHOOT]: this.formService.getUpdateOneShoot.bind(this.formService),
      [EnumProgramMarketingType.DISCOUNT_PRODUCT]: this.formService.getUpdateProductDiscount.bind(this.formService),
      [EnumProgramMarketingType.DISCOUNT_PURCHASE]: this.formService.getUpdateDiscountPurchase.bind(this.formService),
      [EnumProgramMarketingType.PRODUCT_COMPENSATION]: this.formService.getUpdateCompensation.bind(this.formService),
    };

    const getDataMethod = updateMethods[this.params.type as keyof typeof updateMethods];
    if (!getDataMethod) {
      this.isLoadingSubject.next(false);
      return of();
    }

    return getDataMethod(this.programID).pipe(
      tap((resp) => {
        if (resp?.success) this.detailProgram = resp.data as IPayloadProgramMarketing;
      }),
      finalize(() => this.isLoadingSubject.next(false))
    );
  }

  initPageInfo() {
    const isEditMode = !!this.programID && !!this.detailProgram;
    const pageTitle = isEditMode ? 'Edit Program Marketing' : 'Tambah Program Marketing';
    const additionalBreadcrumbs: PageLink[] = isEditMode
      ? [
          {
            title: this.detailProgram.information.program_name,
            path: `sales-marketing/program-marketing/${this.programID}`,
            isActive: false,
          },
          this.pageInfoService.BreadcrumbSeparator,
          {
            title: 'Edit',
            path: '',
            isActive: true,
          },
        ]
      : [{ title: 'Tambah Program', path: '', isActive: true }];

    this.links.push(...additionalBreadcrumbs);
    this.updatePageInfo(pageTitle);
  }

  updatePageInfo(pageTitle: string) {
    this.pageInfoService.updateTitle(pageTitle, undefined, false);
    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  handleCancel() {
    return this.router.navigate(['/sales-marketing/program-marketing/list']);
  }

  handleSubmit() {
    this.modalConfirmation.open();
  }

  getUsePeriod() {
    return this.params.type !== EnumProgramMarketingType.PRODUCT_COMPENSATION;
  }

  validateForm() {
    // console.log('type', this.params.type);
    switch (this.params.type) {
      case EnumProgramMarketingType.ONE_SHOOT:
        // console.log(this.ProgramInfoForm?.valid, this.ProgramTermForm?.valid, this.OrderTermForm?.valid, this.DiscountTermForm?.valid, this.RewardTermForm?.valid);
        return [this.ProgramInfoForm?.valid, this.ProgramTermForm?.valid, this.OrderTermForm?.valid, this.DiscountTermForm?.valid, this.RewardTermForm?.valid].includes(false);
      case EnumProgramMarketingType.DISCOUNT_PRODUCT:
        return [this.ProgramInfoForm?.valid, this.ProgramTermForm?.valid, this.DiscountTermForm?.valid].includes(false);
      case EnumProgramMarketingType.DISCOUNT_PURCHASE:
        return [this.ProgramInfoForm?.valid, this.ProgramTermForm?.valid, this.OrderTermForm?.valid, this.DiscountTermForm?.valid].includes(false);
      case EnumProgramMarketingType.PRODUCT_COMPENSATION:
        return [this.ProgramInfoForm?.valid, this.CompensationTermForm?.valid].includes(false);
      default:
        return true;
    }
  }

  onSubmitForm() {
    this.isLoadingSubject.next(true);
    this.modalResponse.open().then();

    const information = this.programInfoRef && this.programInfoRef.GetPayload;
    const program_term = this.programTermRef && this.programTermRef.GetPayload;
    const discount_term = this.formService.DiscountTermFormPayload;
    const order_term = this.orderTermRef && this.orderTermRef.GetPayload;
    const reward = this.rewardTermRef && this.rewardTermRef.GetPayload;
    const compensation = this.compensationTermRef && this.compensationTermRef.GetPayload;

    switch (this.params.type) {
      case EnumProgramMarketingType.ONE_SHOOT: {
        const payload = { information, program_term, discount_term, order_term, reward };
        this.formService.postTypeOneShoot(payload, this.programID).subscribe((resp) => {
          if (!resp?.data) return;
          this.dataResponse.next(resp.data);
          this.messageResponse.next(resp.message);
          setTimeout(() => {
            this.isLoadingSubject.next(false);
          }, 150);
        });
        break;
      }

      case EnumProgramMarketingType.DISCOUNT_PRODUCT: {
        const payload = { information, program_term, discount_term };
        this.formService.postTypeProductDiscount(payload, this.programID).subscribe((resp) => {
          if (!resp?.data) return;
          this.dataResponse.next(resp.data);
          this.messageResponse.next(resp.message);
          setTimeout(() => {
            this.isLoadingSubject.next(false);
          }, 150);
        });
        break;
      }

      case EnumProgramMarketingType.DISCOUNT_PURCHASE: {
        const payload = { information, program_term, order_term, discount_term };
        this.formService.postTypePurchaseDiscount(payload, this.programID).subscribe((resp) => {
          if (!resp?.data) return;
          this.dataResponse.next(resp.data);
          this.messageResponse.next(resp.message);
          setTimeout(() => {
            this.isLoadingSubject.next(false);
          }, 150);
        });
        break;
      }

      case EnumProgramMarketingType.PRODUCT_COMPENSATION: {
        const payload = { information, compensation };
        this.formService.postTypeProductCompensation(payload, this.programID).subscribe((resp) => {
          if (!resp?.data) return;
          this.dataResponse.next(resp.data);
          this.messageResponse.next(resp.message);
          setTimeout(() => {
            this.isLoadingSubject.next(false);
          }, 150);
        });
        break;
      }
    }

    return true;
  }

  async redirectToListPage() {
    return this.router.navigate(['/sales-marketing/program-marketing/list']);
  }

  async redirectToDetailPage() {
    const id = this.dataResponse.value.program_marketing_id;
    return this.router.navigate(['/sales-marketing/program-marketing/' + id]);
  }

  protected readonly EnumProgramMarketingType = EnumProgramMarketingType;
  protected readonly EnumProgramMarketingPOType = EnumProgramMarketingPOType;
  protected readonly EnumProgramMarketingRewardType = EnumProgramMarketingRewardType;
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly EnumProgramMarketingDiscountCategoryString = EnumProgramMarketingDiscountCategoryString;
  protected readonly EnumProgramMarketingDiscountTypeString = EnumProgramMarketingDiscountTypeString;
  protected readonly EnumProgramMarketingDiscountCategory = EnumProgramMarketingDiscountCategory;
}
