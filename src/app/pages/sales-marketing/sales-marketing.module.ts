import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { DirectiveModule } from '@directives/directive.module';
import { ComponentsModule } from '@shared/components/components.module';
import { DiscountSettingModule } from './discount-setting/discount-setting.module';
import { SalesMarketingRoutingModule } from './sales-marketing-routing.module';
import { ProductsModule } from '../products/products.module';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { ProgramMarketingModule } from '@pages/sales-marketing/program-marketing-legacy/program-marketing.module';
import { MatRadioModule } from '@angular/material/radio';
import { ProgramMarketingRetailerModule } from './program-marketing-retailer/program-marketing-retailer.module';

@NgModule({
  declarations: [],
  exports: [],
  imports: [
    CommonModule,
    FormsModule,
    InlineSVGModule,
    MatTableModule,
    MatSortModule,
    MatProgressSpinnerModule,
    SalesMarketingRoutingModule,
    NgbModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatTabsModule,
    DirectiveModule,
    ComponentsModule,
    DiscountSettingModule,
    ProductsModule,
    MatDatepickerModule,
    MatIconModule,
    MatInputModule,
    MatCheckboxModule,
    ProgramMarketingModule,
    MatRadioModule,
    ProgramMarketingRetailerModule,
  ],
})
export class SalesMarketingModule {}
