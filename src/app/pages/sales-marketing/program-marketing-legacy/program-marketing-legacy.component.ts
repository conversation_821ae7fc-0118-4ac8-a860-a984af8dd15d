import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewChild } from '@angular/core';
import { PageInfoService, PageLink } from '@metronic/layout';
import { BehaviorSubject, Subscription } from 'rxjs';
import { ICardHeaderCounter } from '@shared/components/card/card-header/card-header.model';
import { API } from '@config/constants/api.constant';
import { ActivatedRoute, Router } from '@angular/router';
import { ProgramMarketingService } from './program-marketing.service';
import { BaseDatasource } from '@shared/base/base.datasource';
import { IProgramMarketing } from './program-marketing.interface';
import { UrlUtilsService } from '@utils/url-utils.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { TableColumn } from '@shared/interface/table.interface';
import { UtilitiesService } from '@services/utilities.service';
import { IQueryParams } from '@shared/interface/urlparam.interface';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { AuthService } from '@services/auth.service';
import { ModalProgramSelectorComponent } from './components/modal-program-selector/modal-program-selector.component';
import { EnumProgramMarketingType } from './program-marketing.enum';

@Component({
  selector: 'app-program-marketing',
  templateUrl: './program-marketing-legacy.component.html',
  styleUrls: ['./program-marketing-legacy.component.scss'],
})
export class ProgramMarketingLegacyComponent implements OnInit, OnDestroy {
  // breadcrumbs
  links!: PageLink[];

  baseDataSourceList: BaseDatasource<IProgramMarketing>;
  displayedColumns!: string[];
  tableColumns: TableColumn[] = [
    {
      key: 'program_name',
      title: 'Nama Program',
      isSortable: false,
    },
    {
      key: 'program_type_string',
      title: 'Tipe',
      isSortable: false,
    },
    {
      key: 'scope',
      title: 'Cakupan',
      isSortable: false,
    },
    {
      key: 'period',
      title: 'Periode',

      isSortable: false,
    },
    {
      key: 'status',
      title: 'Status',
      isSortable: false,
    },
    {
      key: 'created_date',
      title: 'Tanggal Dibuat',
      isSortable: false,
    },
    {
      key: 'actions',
      title: 'Action',
      isSortable: false,
    },
  ];

  // filter data
  search = '';
  isActiveFilter = false;

  counterHeader = new BehaviorSubject<ICardHeaderCounter[]>([]);
  private unsubscribe: Subscription[] = [];

  @ViewChild('programTypeSelector') programTypeSelector: ModalProgramSelectorComponent;

  constructor(
    private router: Router,
    private pageInfoService: PageInfoService,
    private activeRoute: ActivatedRoute,
    private urlParamService: UrlUtilsService,
    private baseTableService: BaseTableService<IProgramMarketing>,
    private programMarketingService: ProgramMarketingService,
    public utils: UtilitiesService,
    public authService: AuthService
  ) {}

  ngOnInit(): void {
    this.initPageInfo();
    this.setTableData();
    this.queryHandler();
  }

  initPageInfo() {
    this.links = this.programMarketingService.initProgramMarketingBreadcrumb;
    const _lastIndexOfLink = this.links.length - 1;
    this.links[_lastIndexOfLink].path = '';
    this.pageInfoService.updateShowSidebar(true);
    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  setTableData() {
    const _responseSubs = this.baseTableService.responseDatabase$.subscribe((data) => (this.baseDataSourceList = data));
    this.baseDataSourceList.hasItems = false;
    this.displayedColumns = this.tableColumns.map((item) => item.key);
    this.unsubscribe.push(_responseSubs);
  }

  queryHandler() {
    const _dataSubs = this.activeRoute.queryParams.subscribe((params) => {
      const { string_filter, status, program_type, start_date, end_date } = params;

      this.search = string_filter;
      this.isActiveFilter = !!(status || program_type || start_date || end_date);

      const urlParam = this.urlParamService.sliceQueryParams();
      this.baseTableService.loadDataTable(API.PROGRAM_MARKETING.LIST_PROGRAM, urlParam ?? '');
      this.getCounterList(urlParam);
    });

    this.unsubscribe.push(_dataSubs);
  }

  getCounterList(params = '') {
    const _counterSubs = this.programMarketingService.getCounterList(params).subscribe((resp) => {
      if (!resp) return;
      this.counterHeader.next(resp);
    });

    this.unsubscribe.push(_counterSubs);
  }

  changePageEvent(e: BaseDatasource<IProgramMarketing>) {
    const { pageSize, current_page } = e;
    const dataParams = <IQueryParams>{
      page: current_page,
      size: pageSize,
      string_filter: this.search,
    };

    return this.router.navigate([], {
      queryParams: { ...dataParams },
      queryParamsHandling: 'merge',
    });
  }

  renderThClass(_columnKey: string) {
    return _columnKey === 'actions'
      ? 'min-w-70px text-center'
      : _columnKey === 'program_name'
      ? 'min-w-200px mw-lg-300px'
      : _columnKey === 'program_type_string'
      ? 'mw-125px'
      : _columnKey === 'period' || _columnKey === 'created_date'
      ? 'mw-100px'
      : _columnKey === 'status_name' || _columnKey === 'scope'
      ? 'mw-200px'
      : 'min-w-150px';
  }

  isEmptyData = () => !this.search && !this.isActiveFilter && this.baseDataSourceList.isTableLoaded && !this.baseDataSourceList.hasItems;

  searchNotFound = () => this.baseDataSourceList.isTableLoaded && this.baseDataSourceList.totalItem === 0 && !!(this.search || this.isActiveFilter);

  goToDetail = (id: string) => this.router.navigate([`/sales-marketing/program-marketing/${id}`]);

  hasPeriodRange(e: IProgramMarketing) {
    return !!(e.period_start && e.period_end);
  }

  handleSelectedProgram(e: EnumProgramMarketingType) {
    return this.router.navigate(['/sales-marketing/program-marketing/form'], {
      queryParams: {
        type: e,
      },
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
