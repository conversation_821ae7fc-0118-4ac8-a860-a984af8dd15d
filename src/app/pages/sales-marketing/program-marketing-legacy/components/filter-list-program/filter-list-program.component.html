<app-input-search (actionFilter)="actionSearch($event)" [isFinishLoadingSubject]="finishLoadingSubject" [value]="searchInputValue" placeholder="Cari nama program" />
<div class="ms-auto position-relative">
  <div class="d-flex align-items-center justify-content-center">
    <ng-container *ngIf="getPrivilegeCTACreatePromag()">
      <app-table-content [height]="44" [isFinishLoadingSubject]="finishLoadingSubject" [width]="142">
        <app-button-add-program (selectedProgramType)="onSelectedProgram($event)" />
      </app-table-content>
    </ng-container>

    <div class="ms-3">
      <app-filter-table
        (actionClick)="toggleOpenFilter()"
        (actionReset)="actionResetFilter()"
        [isActiveFilter]="filterInputActivated"
        [isFinishLoadingSubject]="finishLoadingSubject"
        [isOpenFilter]="filterInputOpened"
        [resetLabel]="'Reset Filter'"
      >
        <div class="menu menu-sub menu-sub-dropdown w-300px w-md-350px show filter-body" id="kt-toolbar-filter">
          <div class="px-7 py-5">
            <div class="d-flex align-items-center justify-content-between">
              <div class="fs-4 text-dark fw-bold">Filter</div>
              <div>
                <span (click)="toggleOpenFilter()" [inlineSVG]="STRING_CONSTANTS.ICON.IC_CLOSE_MODAL" class="svg-icon svg-icon2 cursor-pointer"></span>
              </div>
            </div>
          </div>

          <div class="separator border-gray-300"></div>

          <form (ngSubmit)="actionSubmitFilter()" [formGroup]="filterForm" class="form w-100">
            <div class="px-7 py-5">
              <!-- tipe program -->
              <div class="mb-10">
                <label class="form-label">Tipe Program Marketing</label>
                <div class="filter-pills">
                  <mat-chip-listbox #filterPillsBoxType (change)="actionStatusChange($event, 'program')" multiple>
                    <ng-container formArrayName="program_type">
                      <mat-chip-option *ngFor="let item of filterPillsType" [value]="item" class="chips">
                        <div class="d-flex align-items-center justify-content-between">
                          <span>{{ item }}</span>
                          <span class="custom-x-icon"></span>
                        </div>
                      </mat-chip-option>
                    </ng-container>
                  </mat-chip-listbox>
                </div>
              </div>

              <!-- status -->
              <div class="mb-10">
                <label class="form-label">Status</label>
                <div class="filter-pills">
                  <mat-chip-listbox #filterPillsBoxStatus (change)="actionStatusChange($event, 'status')" multiple>
                    <ng-container formArrayName="status">
                      <mat-chip-option *ngFor="let item of filterPillsStatus" [value]="item" class="chips">
                        <div class="d-flex align-items-center justify-content-between">
                          <span>{{ item }}</span>
                          <span class="custom-x-icon"></span>
                        </div>
                      </mat-chip-option>
                    </ng-container>
                  </mat-chip-listbox>
                </div>
              </div>

              <div class="mb-10">
                <label class="form-label">Periode</label>
                <app-date-range-picker [formGroup]="filterForm" [nameEnd]="'end_date'" [nameStart]="'start_date'" [placeholder]="'Pilih rentang periode program'" />
              </div>

              <div class="d-flex justify-content-end">
                <button (click)="actionResetFilter()" [disabled]="validateFilter()" class="btn btn-outline me-4" mat-stroked-button type="reset">
                  <span class="text-primary">Reset</span>
                </button>
                <button [disabled]="validateFilter()" class="btn btn-primary" color="primary" mat-raised-button type="submit">
                  <span class="text-white">Terapkan</span>
                </button>
              </div>
            </div>
          </form>
        </div>
      </app-filter-table>
    </div>
  </div>
</div>
