import { AfterViewInit, Component, EventEmitter, HostBinding, Input, OnInit, Output, ViewChild } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatChipListbox, MatChipListboxChange } from '@angular/material/chips';
import { EnumProgramMarketingStatusString, EnumProgramMarketingType, EnumProgramMarketingTypeString } from '../../program-marketing.enum';
import { UtilitiesService } from '@services/utilities.service';
import { FilterService } from '@services/filter.service';
import { AuthService } from '@services/auth.service';
import { RolePrivilegeService } from '@services/role-privilege.service';

@Component({
  selector: 'app-filter-list-program',
  templateUrl: './filter-list-program.component.html',
  styleUrls: ['./filter-list-program.component.scss'],
})
export class FilterListProgramComponent implements OnInit, AfterViewInit {
  @HostBinding('class') class = 'd-flex w-100 justify-content-between';

  @Input() finishLoadingSubject = new BehaviorSubject(true);
  @Input() searchInputValue: string = '';
  @Input() filterInputActivated = false;

  filterForm: FormGroup;
  filterInputOpened = false;
  filterPillsStatus: string[] = [];
  filterPillsType: string[] = [];

  currentDate = new Date();
  activeRouteSnapshot!: ActivatedRouteSnapshot;

  @ViewChild('filterPillsBoxStatus') filterPillsBoxStatus: MatChipListbox;
  @ViewChild('filterPillsBoxType') filterPillsBoxType: MatChipListbox;

  @Output() actionAddProgram = new EventEmitter<EnumProgramMarketingType>();

  constructor(
    private activatedRoute: ActivatedRoute,
    private fb: FormBuilder,
    private utils: UtilitiesService,
    private filterService: FilterService,
    private rolePrivilegeService: RolePrivilegeService,
    public authService: AuthService,
  ) {
    this.filterForm = this.fb.group({
      program_type: new FormControl(''),
      status: new FormControl(''),
      start_date: new FormControl({ value: '', disabled: true }),
      end_date: new FormControl({ value: '', disabled: true }),
    });
  }

  get statusControl() {
    return <FormControl>this.filterForm.get('status');
  }

  get typeControl() {
    return <FormControl>this.filterForm.get('program_type');
  }

  get periodStartControl() {
    return <FormControl>this.filterForm.get('start_date');
  }

  get periodEndControl() {
    return <FormControl>this.filterForm.get('end_date');
  }

  getPrivilegeCTACreatePromag() {
    return this.rolePrivilegeService.checkPrivilege('SALES_MARKETING_PROGRAM', 'PROGRAM_MARKETING_SETTING', 'CTA_CREATE_PROGRAM');
  }

  ngOnInit() {
    this.activeRouteSnapshot = this.activatedRoute.snapshot;
    this.initFilter();
  }

  initFilter() {
    const _keysStatus = Object.keys(EnumProgramMarketingStatusString);
    const _keysType = Object.keys(EnumProgramMarketingTypeString).filter((key) => key !== 'ONE_SHOOT'); // temp.fix inconsistent response key name;
    this.filterPillsType = this.filterPillsEnumMapper(_keysType, Object(EnumProgramMarketingTypeString));
    this.filterPillsStatus = this.filterPillsEnumMapper(_keysStatus, Object(EnumProgramMarketingStatusString));
  }

  filterPillsEnumMapper(keys: string[], objEnum: any) {
    return keys.map((_item) => objEnum[_item]);
  }

  initFilterPillsBox() {
    const { queryParams } = this.activeRouteSnapshot;
    const queryParamIsEmpty = !Object.keys(queryParams).length;

    if (queryParamIsEmpty) return;

    const _arrayStatus = 'status' in queryParams ? queryParams.status.split(',') : [];
    const _arrayType = 'type' in queryParams ? queryParams.type.split(',') : [];

    const _selectedStatus: string[] = [];

    if (!!_arrayStatus.length) {
      _arrayStatus.forEach((_status: string) => {
        const value = this.utils.mapKeyToString(EnumProgramMarketingStatusString, _status);
        _selectedStatus.push(value);
      });

      this.filterPillsBoxStatus.value = _selectedStatus;
      this.statusControl.patchValue(_selectedStatus);
    }

    if (!!_arrayType.length) {
      _arrayType.forEach((_type: string) => {
        const value = this.utils.mapKeyToString(EnumProgramMarketingTypeString, _type);
        _selectedStatus.push(value);
      });

      this.filterPillsBoxType.value = _selectedStatus;
      this.typeControl.patchValue(_selectedStatus);
    }
  }

  actionSearch(e: string) {
    this.filterService.onSearch(e);
    this.searchInputValue = '';
  }

  actionSubmitFilter() {
    this.filterInputOpened = false;

    const _extras = {
      queryParams: {
        string_filter: this.searchInputValue,
        program_type: !!this.typeControl.value ? this.typeControl.value : undefined,
        status: !!this.statusControl.value ? this.statusControl.value : undefined,
        start_date: this.utils.timeStampToDate(this.periodStartControl.value, 'yyyy-MM-dd'),
        end_date: this.utils.timeStampToDate(this.periodEndControl.value, 'yyyy-MM-dd'),
      },
    };

    this.filterService.submitFilter(this.filterForm, _extras);
  }

  actionResetFilter() {
    this.filterInputOpened = false;
    this.resetFilterForm();
  }

  actionStatusChange(e: MatChipListboxChange, type: 'status' | 'program') {
    if (!type) return;

    const _selectedKey: string[] = [];

    // todo: create method mapper selection pills changes by type
    if (type === 'status') {
      e.value.forEach((item: string) => _selectedKey.push(this.utils.getEnumKeyByValue(EnumProgramMarketingStatusString, item)));
      this.statusControl.patchValue(_selectedKey.join());
    }

    if (type === 'program') {
      e.value.forEach((item: string) => _selectedKey.push(this.utils.getEnumKeyByValue(EnumProgramMarketingTypeString, item)));
      this.typeControl.patchValue(_selectedKey.join());
    }
  }

  onSelectedProgram(e: EnumProgramMarketingType) {
    this.actionAddProgram.emit(e);
  }

  toggleOpenFilter = () => (this.filterInputOpened = !this.filterInputOpened);

  resetFilterForm() {
    this.filterService.resetFilter(this.filterForm, ['status', 'program_type', 'start_date', 'end_date']);
    this.resetFilterPillsBox();
  }

  resetFilterPillsBox = () => {
    this.filterPillsBoxStatus.value = [];
    this.filterPillsBoxType.value = [];
  };

  ngAfterViewInit(): void {
    this.initFilterPillsBox();
  }

  validateFilter() {
    return !(!!this.statusControl.value || !!this.typeControl.value || (!!this.periodStartControl.value && !!this.periodEndControl.value));
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
