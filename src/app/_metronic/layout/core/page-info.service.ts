import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { BaseDatasource } from '@shared/base/base.datasource';

export interface PageLink {
  title?: string | null;
  path: string;
  isActive: boolean;
  isSeparator?: boolean;
  attributes?: string;
  queryParams?: {};
}

export class PageInfo {
  breadcrumbs: Array<PageLink> = [];
  title: string = '';
}

@Injectable({
  providedIn: 'root',
})
export class PageInfoService {
  // public title: BehaviorSubject<string> = new BehaviorSubject<string>('Dashboard');
  public title: BehaviorSubject<string> = new BehaviorSubject<string>('');
  public description: BehaviorSubject<string> = new BehaviorSubject<string>('');
  public breadcrumbs: BehaviorSubject<Array<PageLink>> = new BehaviorSubject<Array<PageLink>>([]);
  public showSidebar: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  constructor() {
  }

  public setTitle(_title: string) {
    this.title.next(_title);
  }

  public updateTitle<T>(_title: string, baseDatasource?: BaseDatasource<T>, showSidebar?: boolean) {
    setTimeout(() => {
      this.setTitle(_title);

      if (baseDatasource) {
        this.setTitle(_title + ' (' + baseDatasource.totalItem + ')');
      }

      this.setShowSidebar(showSidebar === false ? showSidebar : true);
    }, 100);
  }

  public setShowSidebar(_showSidebar: boolean) {
    this.showSidebar.next(_showSidebar);
  }

  public updateShowSidebar(_showSidebar: boolean) {
    setTimeout(() => {
      this.showSidebar.next(_showSidebar);
    }, 1);
  }

  public setDescription(_title: string) {
    this.description.next(_title);
  }

  public updateDescription(_description: string) {
    setTimeout(() => {
      this.setDescription(_description);
    }, 1);
  }

  public setBreadcrumbs(_bs: Array<PageLink>) {
    this.breadcrumbs.next(_bs);
  }

  public updateBreadcrumbs(_bs: Array<PageLink>, _attributes: any = null) {
    setTimeout(() => {
      this.setBreadcrumbs(_bs);
    }, 10);
  }

  public calculateTitle() {
    const asideTitle = this.calculateTitleInMenu('kt_app_sidebar');
    const headerTitle = this.calculateTitleInMenu('kt_app_header_wrapper');
    const title = asideTitle || headerTitle || '';
    this.setTitle(title);
  }

  public calculateTitleInMenu(menuId: string): string | undefined {
    const menu = document.getElementById(menuId);
    if (!menu) {
      return;
    }

    const allActiveMenuLinks = Array.from<HTMLLinkElement>(menu.querySelectorAll('a.menu-link')).filter((link) => link.classList.contains('active'));

    if (!allActiveMenuLinks || allActiveMenuLinks.length === 0) {
      return;
    }

    const titleSpan = allActiveMenuLinks[0].querySelector('span.menu-title') as HTMLSpanElement | null;
    if (!titleSpan) {
      return;
    }

    return titleSpan.innerText;
  }

  public calculateBreadcrumbs() {
    const asideBc = this.calculateBreadcrumbsInMenu('asideMenu');
    const headerBc = this.calculateBreadcrumbsInMenu('#kt_header_menu');
    const bc = asideBc && asideBc.length > 0 ? asideBc : headerBc;

    if (!bc) {
      this.setBreadcrumbs([]);
      return;
    }

    this.setBreadcrumbs(bc);
  }

  public calculateBreadcrumbsInMenu(menuId: string): Array<PageLink> | undefined {
    const result: Array<PageLink> = [];
    const menu = document.getElementById(menuId);

    if (!menu) {
      return;
    }

    const allActiveParents = Array.from<HTMLDivElement>(menu.querySelectorAll('div.menu-item')).filter((link) => link.classList.contains('here'));

    if (!allActiveParents || allActiveParents.length === 0) {
      return;
    }

    allActiveParents.forEach((parent) => {
      const titleSpan = parent.querySelector('span.menu-title') as HTMLSpanElement | null;
      if (!titleSpan) {
        return;
      }

      const title = titleSpan.innerText;
      const path = titleSpan.getAttribute('data-link');
      if (!path) {
        return;
      }

      result.push({
        title,
        path,
        isSeparator: false,
        isActive: false,
      });
      // add separator
      result.push({
        title: '',
        path: '',
        isSeparator: true,
        isActive: false,
      });
    });

    return result;
  }

  get BreadcrumbSeparator() {
    return <PageLink>{ title: '', path: '', isActive: false, isSeparator: true };
  }
}
